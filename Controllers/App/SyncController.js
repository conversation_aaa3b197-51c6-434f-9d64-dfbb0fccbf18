const { Op, Transaction } = require('sequelize');
const {
  BUTTON_TYPES,
  OPERATION_TYPES,
  BUTTON_TYPES_NUMBERS,
  TAG_TYPES,
} = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();
const TagModel = new (require('../../Models/App/TagModel'))();
const AlarmModel = new (require('../../Models/App/AlarmModel'))();

class SyncController {
  #buttonIdMap = new Map();
  #itemIdMap = new Map();
  #tagIdMap = new Map();
  #alarmIdMap = new Map();

  // Cache for database lookups to reduce N+1 queries
  #buttonCache = new Map();
  #itemCache = new Map();
  #tagCache = new Map();
  #alarmCache = new Map();

  // Performance tracking
  #performanceMetrics = {
    totalOperations: 0,
    operationsByType: new Map(),
    startTime: null,
    endTime: null,
    dbQueries: 0,
    bulkOperations: 0,
    cacheHits: 0,
    parallelOperations: 0,
  };

  #resetMaps() {
    this.#buttonIdMap.clear();
    this.#itemIdMap.clear();
    this.#tagIdMap.clear();
    this.#alarmIdMap.clear();

    // Clear caches
    this.#buttonCache.clear();
    this.#itemCache.clear();
    this.#tagCache.clear();
    this.#alarmCache.clear();

    // Reset performance metrics
    this.#performanceMetrics = {
      totalOperations: 0,
      operationsByType: new Map(),
      startTime: null,
      endTime: null,
      dbQueries: 0,
      bulkOperations: 0,
      cacheHits: 0,
      parallelOperations: 0,
    };
  }

  // Performance-optimized logging (only in development)
  // #log(message, level = 'info') {
  //   if (process.env.NODE_ENV === 'development' || level === 'error') {
  //     console.log(`[SyncController] ${message}`);
  //   }
  // }

  // Batch processing for memory optimization
  // #processBatch(items, batchSize = 100) {
  //   const batches = [];
  //   for (let i = 0; i < items.length; i += batchSize) {
  //     batches.push(items.slice(i, i + batchSize));
  //   }
  //   return batches;
  // }

  // Helper method to check if request is aborted
  #checkRequestAborted(req) {
    if (req.aborted || req.socket?.destroyed) {
      const error = new Error('Request was aborted by client');
      error.code = 'REQUEST_ABORTED';
      error.message = 'Request was aborted by client';
      throw error;
    }
  }

  // Create AbortController for internal operations
  #createAbortController(req) {
    const controller = new AbortController();

    // Listen for client disconnect
    const onAbort = () => {
      console.log('Client disconnected, aborting operations...');
      controller.abort();
    };

    req.on('aborted', onAbort);
    req.on('close', onAbort);
    req.socket?.on('close', onAbort);

    // Cleanup listeners when done
    const cleanup = () => {
      req.removeListener('aborted', onAbort);
      req.removeListener('close', onAbort);
      req.socket?.removeListener('close', onAbort);
    };

    return { controller, cleanup };
  }

  // Helper methods for error handling
  #getErrorMessage(error) {
    if (error.parent?.code === 'ER_SERVER_SHUTDOWN') {
      return 'Database server is shutting down. Please try again later.';
    }
    if (error.parent?.code === 'PROTOCOL_CONNECTION_LOST') {
      return 'Database connection lost. Please try again.';
    }
    if (error.code === 'ER_SERVER_SHUTDOWN') {
      return 'Database server is shutting down. Please try again later.';
    }
    if (error.code === 'PROTOCOL_CONNECTION_LOST') {
      return 'Database connection lost. Please try again.';
    }
    return error.message || 'An unexpected error occurred during sync';
  }

  #getErrorCode(error) {
    if (error.parent?.code) {
      return error.parent.code;
    }
    if (error.code) {
      return error.code;
    }
    return 'SYNC_ERROR';
  }

  #isRetryableError(error) {
    const retryableCodes = [
      'ER_SERVER_SHUTDOWN',
      'PROTOCOL_CONNECTION_LOST',
      'ER_LOCK_WAIT_TIMEOUT',
      'ER_LOCK_DEADLOCK',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
    ];

    return (
      retryableCodes.includes(error.code) ||
      retryableCodes.includes(error.parent?.code)
    );
  }

  // Check database connection health
  async #checkDatabaseConnection() {
    try {
      // Use the global sequelize instance
      await global.sequelize.authenticate();
      return true;
    } catch (error) {
      console.error(
        '[SyncController] Database connection check failed:',
        error
      );
      return false;
    }
  }

  // Group operations by type for potential bulk processing
  #groupOperationsByType(operations) {
    const grouped = {
      buttons: { create: [], update: [], delete: [] },
      items: { create: [], update: [], delete: [] },
      tags: { create: [], update: [], delete: [] },
      alarms: { update: [] },
      sequences: { update: [] },
    };

    operations.forEach((operation, index) => {
      // Track operation types for metrics
      const opType = operation.operationType;
      this.#performanceMetrics.operationsByType.set(
        opType,
        (this.#performanceMetrics.operationsByType.get(opType) || 0) + 1
      );

      // Add index for error tracking
      const opWithIndex = { ...operation, originalIndex: index };

      switch (operation.operationType) {
        case OPERATION_TYPES.ADD_BUTTON:
          grouped.buttons.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_BUTTON:
          grouped.buttons.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_BUTTON:
          grouped.buttons.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.CREATE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_VALUE_BUTTON_ITEM:
          grouped.items.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
          grouped.items.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
          grouped.items.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.CREATE_ITEM_TAG:
          grouped.tags.create.push(opWithIndex);
          break;
        case OPERATION_TYPES.EDIT_ITEM_TAG:
          grouped.tags.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.DELETE_ITEM_TAG:
          grouped.tags.delete.push(opWithIndex);
          break;
        case OPERATION_TYPES.UPDATE_BUTTON_ALARM:
          grouped.alarms.update.push(opWithIndex);
          break;
        case OPERATION_TYPES.CHANGE_BUTTON_SEQUENCE:
          grouped.sequences.update.push(opWithIndex);
          break;
      }
    });

    return grouped;
  }

  // Process operations in optimized order with bulk operations where possible
  async #processOperationsOptimized(
    groupedOperations,
    userId,
    transaction,
    abortController,
    req
  ) {
    const responseData = [];

    // Process in dependency order: Buttons -> Items -> Tags -> Alarms -> Sequences

    // 1. Process Button Operations
    console.log('[SyncController] Starting button operations...');
    await this.#processBulkButtonOperations(
      groupedOperations.buttons,
      userId,
      transaction,
      responseData,
      abortController,
      req
    );
    console.log('[SyncController] Completed button operations.');

    // 2. Process Item Operations and independent operations in parallel
    console.log(
      '[SyncController] Starting parallel operations (items, alarms, sequences)...'
    );

    const parallelPromises = [];

    // Items depend on buttons, so process after buttons
    if (
      groupedOperations.items.create.length > 0 ||
      groupedOperations.items.update.length > 0 ||
      groupedOperations.items.delete.length > 0
    ) {
      parallelPromises.push(
        this.#processBulkItemOperations(
          groupedOperations.items,
          userId,
          transaction,
          responseData,
          abortController,
          req
        )
      );
    }

    // Alarms and sequences can run in parallel with items (they only depend on buttons)
    if (
      groupedOperations.alarms.create.length > 0 ||
      groupedOperations.alarms.update.length > 0 ||
      groupedOperations.alarms.delete.length > 0
    ) {
      parallelPromises.push(
        this.#processBulkAlarmOperations(
          groupedOperations.alarms,
          userId,
          transaction,
          responseData,
          abortController,
          req
        )
      );
    }

    if (groupedOperations.sequences.length > 0) {
      parallelPromises.push(
        this.#processBulkSequenceOperations(
          groupedOperations.sequences,
          userId,
          transaction,
          responseData,
          abortController,
          req
        )
      );
    }

    // Execute parallel operations
    if (parallelPromises.length > 0) {
      await Promise.all(parallelPromises);
      this.#performanceMetrics.parallelOperations += parallelPromises.length;
    }

    console.log('[SyncController] Completed parallel operations.');

    // 3. Process Tag Operations last (depends on items)
    console.log('[SyncController] Starting tag operations...');
    await this.#processBulkTagOperations(
      groupedOperations.tags,
      userId,
      transaction,
      responseData,
      abortController,
      req
    );
    console.log('[SyncController] Completed tag operations.');

    // Sort response data by original operation order
    responseData.sort((a, b) => a.originalIndex - b.originalIndex);

    return responseData.map((item) => {
      const { originalIndex, ...response } = item;
      return response;
    });
  }

  // Bulk process button operations
  async #processBulkButtonOperations(
    buttonOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    console.log('[SyncController] Processing button operations...');

    // Process creates first (bulk create)
    if (buttonOps.create.length > 0) {
      console.log(
        `[SyncController] Attempting bulk create for ${buttonOps.create.length} buttons.`
      );
      await this.#bulkCreateButtons(
        buttonOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );
      console.log(`[SyncController] Finished bulk create for buttons.`);
    }

    // Process updates individually (need to check existing buttons)
    for (const operation of buttonOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn('[SyncController] Request aborted during button update.');
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Updating button sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}`
        );
        await this.updateButtonSync(operation, userId, transaction);
        this.#addButtonResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully updated button sync for localButtonId: ${operation.localButtonId}`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error updating button sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}:`,
          error.message
        );
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }
    }

    // Process deletes individually (need to check existing buttons)
    for (const operation of buttonOps.delete) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn('[SyncController] Request aborted during button delete.');
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Deleting button sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}`
        );
        await this.deleteButtonSync(operation, userId, transaction);
        this.#addButtonResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully deleted button sync for localButtonId: ${operation.localButtonId}`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error deleting button sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}:`,
          error.message
        );
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }
    }
    console.log('[SyncController] Finished processing button operations.');
  }

  // Bulk create buttons for better performance
  async #bulkCreateButtons(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) {
      console.log('[SyncController] No buttons to bulk create.');
      return;
    }

    const bulkPayload = [];

    const updateOnDuplicateFields = [
      'button_name',
      'button_color',
      'button_type',
      'button_shape',
      'button_summery_calculation',
      'button_sequence',
      'alarm_tag',
      'text_note_tag',
      'location_tag',
    ];

    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn(
          '[SyncController] Request aborted during bulk button payload preparation.'
        );
        throw new Error('Request was aborted by client');
      }

      const data = {
        ...operation.payload,
        userId,
        localButtonId: operation.localButtonId,
      };
      const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

      if (!buttonType) {
        console.error(
          `[SyncController] Invalid button type for operation syncId: ${operation.syncId}, localButtonId: ${operation.localButtonId}.`
        );
        const error = new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }

      const payload = {
        user_id: userId,
        button_uuid: data.buttonUuid,
        button_name: data.buttonName,
        button_color: data.buttonColor,
        button_type: buttonType,
        button_shape: data.buttonShape,
        button_summery_calculation: JSON.stringify(
          data.buttonSummeryCalculation
        ),
        button_sequence: data.buttonSequence,
        // Add metadata for tracking
        _localButtonId: operation.localButtonId,
        _originalIndex: operation.originalIndex,
        _operationType: operation.operationType,
      };

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.count_inc = Number(data.countInc);
        updateOnDuplicateFields.push('count_inc');
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.value_unit = data.valueUnit || null;
        payload.value_unit_description = data.valueUnitDescription || null;
        payload.value_item_name = data.valueItemName;
        updateOnDuplicateFields.push(
          'value_unit',
          'value_unit_description',
          'value_item_name'
        );
      }

      // Add optional tag fields
      if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
      if (data.textNoteTag !== undefined)
        payload.text_note_tag = data.textNoteTag;
      if (data.locationTag !== undefined)
        payload.location_tag = data.locationTag;

      bulkPayload.push(payload);
    }

    try {
      console.log(
        `[SyncController] Executing ButtonModel.createBulkButtons for ${bulkPayload.length} records.`
      );
      // Use bulk create with returning option to get IDs
      const createdButtons = await ButtonModel.createBulkButtons(bulkPayload, {
        transaction,
        returning: true,
        updateOnDuplicate: updateOnDuplicateFields,
      });

      this.#performanceMetrics.bulkOperations++;
      this.#performanceMetrics.dbQueries++;
      console.log(
        `[SyncController] Successfully bulk created ${createdButtons.length} buttons.`
      );

      // Map created buttons to local IDs and prepare bulk alarm creation
      const alarmPayloads = [];
      const updateOnDuplicateAlarmFields = [
        'alarm_time',
        'snooze_time',
        'repeat_daily',
        'is_ring_after',
        'is_ring_after_always',
        'ring_after_time_stamp',
        'ring_after_time_ms',
        'is_active',
      ];
      for (let i = 0; i < createdButtons.length; i++) {
        const button = createdButtons[i];
        const originalOp = createOps[i];

        this.#buttonIdMap.set(originalOp.localButtonId, button.id);
        this.#alarmIdMap.set(button.id, button.id);

        // Prepare alarm payload for bulk creation
        alarmPayloads.push({
          button_id: button.id,
          user_id: userId,
          button_uuid: button.button_uuid,
          alarm_time: null,
          snooze_time: null,
          repeat_daily: false,
          is_ring_after: false,
          is_ring_after_always: false,
          ring_after_time_stamp: null,
          ring_after_time_ms: null,
          is_active: false,
        });

        // Add to response data
        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: button.id,
          alarmId: button.id,
          itemId: null,
          tagId: null,
          localButtonId: originalOp.localButtonId,
          localItemId: null,
          localTagId: null,
          originalIndex: originalOp.originalIndex,
          buttonUuid: button.button_uuid,
          itemUuid: null,
          tagUuid: null,
        });
      }

      // Bulk create alarms for all buttons
      if (alarmPayloads.length > 0) {
        await AlarmModel.createBulkAlarms(alarmPayloads, {
          transaction,
          updateOnDuplicate: updateOnDuplicateAlarmFields,
        });
        this.#performanceMetrics.bulkOperations++;
        this.#performanceMetrics.dbQueries++;
      }
      console.log(
        `[SyncController] All bulk created buttons processed and responses added.`
      );
    } catch (error) {
      // If bulk create fails, fall back to individual creates for better error handling
      console.warn(
        '[SyncController] Bulk button create failed, falling back to individual creates:',
        error.message
      );
      // await this.#fallbackIndividualButtonCreates(
      //   createOps,
      //   userId,
      //   transaction,
      //   responseData,
      //   abortController,
      //   req
      // );
    }
  }

  // Helper method to create alarm for a button
  // async #createAlarmForButton(button, userId, transaction) {
  //   console.log(
  //     `[SyncController] Preparing to create alarm for button ID: ${button.id}`
  //   );
  //   const alarmPayload = {
  //     button_id: button.id,
  //     user_id: userId,
  //     button_uuid: button.button_uuid,
  //     alarm_time: null,
  //     snooze_time: null,
  //     repeat_daily: false,
  //     is_ring_after: false,
  //     is_ring_after_always: false,
  //     ring_after_time_stamp: null,
  //     ring_after_time_ms: null,
  //     is_active: false,
  //   };

  //   await AlarmModel.createAlarm(alarmPayload, { transaction });
  //   this.#performanceMetrics.dbQueries++;
  //   console.log(
  //     `[SyncController] Alarm created successfully for button ID: ${button.id}`
  //   );
  // }

  // Fallback method for individual button creates
  // async #fallbackIndividualButtonCreates(
  //   createOps,
  //   userId,
  //   transaction,
  //   responseData,
  //   abortController,
  //   req
  // ) {
  //   console.log(
  //     '[SyncController] Falling back to individual button creates...'
  //   );
  //   for (const operation of createOps) {
  //     this.#checkRequestAborted(req);
  //     if (abortController.signal.aborted) {
  //       console.warn(
  //         '[SyncController] Request aborted during individual button create fallback.'
  //       );
  //       throw new Error('Request was aborted by client');
  //     }

  //     try {
  //       console.log(
  //         `[SyncController] Creating button individually for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}`
  //       );
  //       await this.createButtonSync(operation, userId, transaction);
  //       this.#addButtonResponse(operation, responseData);
  //       console.log(
  //         `[SyncController] Successfully created button individually for localButtonId: ${operation.localButtonId}`
  //       );
  //     } catch (error) {
  //       console.error(
  //         `[SyncController] Error creating button individually for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}:`,
  //         error.message
  //       );
  //       error.operationIndex = operation.originalIndex;
  //       error.operationType = operation.operationType;
  //       throw error;
  //     }
  //   }
  //   console.log(
  //     '[SyncController] Finished individual button creates fallback.'
  //   );
  // }

  // Helper to add button response data
  #addButtonResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    console.log(
      `[SyncController] Adding button response for operationType: ${operation.operationType}, syncId: ${operation.syncId}, btnId: ${buttonId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: null,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process item operations with optimized bulk creates
  async #processBulkItemOperations(
    itemOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    // Process creates first with bulk operation
    if (itemOps.create.length > 0) {
      await this.#bulkCreateItems(
        itemOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );
    }

    // Process updates and deletes individually (need to check existing items)
    const individualOps = [...itemOps.update, ...itemOps.delete];

    if (individualOps.length > 0) {
      // Pre-fetch all required buttons to reduce N+1 queries
      await this.#preloadButtonsForItems(individualOps, userId, transaction);

      for (const operation of individualOps) {
        this.#checkRequestAborted(req);
        if (abortController.signal.aborted) {
          throw new Error('Request was aborted by client');
        }

        try {
          switch (operation.operationType) {
            case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
            case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
            case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
              await this.updateItemSync(operation, userId, transaction);
              break;
            case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
            case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
            case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
              await this.deleteItemSync(operation, userId, transaction);
              break;
          }
          this.#addItemResponse(operation, responseData);
        } catch (error) {
          error.operationIndex = operation.originalIndex;
          error.operationType = operation.operationType;
          throw error;
        }
      }
    }
  }

  // Bulk create items for better performance
  async #bulkCreateItems(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) return;

    // Pre-validate all buttons exist and cache them
    const buttonUuids = new Set();
    createOps.forEach((op) => {
      const buttonUuid = op.buttonUuid;

      if (buttonUuid) buttonUuids.add(buttonUuid);
    });

    if (buttonUuids.size > 0) {
      const buttons = await ButtonModel.findButtons(
        { button_uuid: { [Op.in]: Array.from(buttonUuids) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;

      // Cache buttons for validation using both ID and UUID
      buttons.forEach((button) => {
        this.#buttonCache.set(button.id, button);
        this.#buttonCache.set(button.button_uuid, button);
      });
    }

    const bulkPayload = [];

    const updateOnDuplicateItemsFields = [
      'count_value',
      'count_time_stamp',
      'duration_start_time_stamp',
      'duration_stop_time_stamp',
      'duration_time_ms',
      'item_name',
      'item_value',
      'value_unit',
      'value_time_stamp',
      'display_time',
      'display_date',
      'display_month_year',
    ];

    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        throw new Error('Request was aborted by client');
      }

      const data = {
        ...operation.payload,
        userId,
        localItemId: operation.localItemId,
      };
      const buttonUuid = operation.buttonUuid;
      const button = this.#buttonCache.get(buttonUuid);

      if (!button) {
        const error = new Error(API.BUTTON_NOT_FOUND);
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }

      const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];
      if (!buttonType || buttonType !== button.button_type) {
        const error = new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }

      const payload = {
        button_id: button.id,
        user_id: userId,
        item_uuid: data.itemUuid,
        display_time: data.displayTime,
        display_date: data.displayDate,
        display_month_year: data.displayMonthYear,
        _localItemId: operation.localItemId,
        _originalIndex: operation.originalIndex,
      };

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.count_value = data.countIncrement;
        payload.count_time_stamp = data.countTimeStamp;
      } else if (buttonType === BUTTON_TYPES.DURATION) {
        payload.duration_start_time_stamp = data.durationStartTimeStamp;
        payload.duration_stop_time_stamp = data.durationStopTimeStamp;
        payload.duration_time_ms = data.durationTimeMs;
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.item_name = data.itemName;
        payload.item_value = data.itemValue;
        payload.value_unit = data.valueUnit;
        payload.value_time_stamp = data.valueTimeStamp;
      }

      bulkPayload.push(payload);
    }

    try {
      // Use Sequelize bulkCreate directly on the schema
      const { Item: ItemSchema } = require('../../Database/Schemas');
      const createdItems = await ItemSchema.bulkCreate(bulkPayload, {
        transaction,
        returning: true,
        validate: true,
        updateOnDuplicate: updateOnDuplicateItemsFields,
      });

      this.#performanceMetrics.bulkOperations++;
      this.#performanceMetrics.dbQueries++;

      // Map created items to local IDs and add responses
      for (let i = 0; i < createdItems.length; i++) {
        const item = Array.isArray(createdItems)
          ? createdItems[i]
          : createdItems;
        const originalOp = createOps[i];

        this.#itemIdMap.set(originalOp.localItemId, item.id);

        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: item.button_id,
          alarmId: this.#alarmIdMap.get(item.button_id) || null,
          itemId: item.id,
          tagId: null,
          localButtonId: originalOp.localButtonId,
          localItemId: originalOp.localItemId,
          localTagId: null,
          originalIndex: originalOp.originalIndex,
        });
      }
    } catch (error) {
      throw error;
      // Fallback to individual creates if bulk fails
      // for (const operation of createOps) {
      //   try {
      //     await this.createItemSync(operation, userId, transaction);
      //     this.#addItemResponse(operation, responseData);
      //   } catch (individualError) {
      //     individualError.operationIndex = operation.originalIndex;
      //     individualError.operationType = operation.operationType;
      //     throw individualError;
      //   }
      // }
    }
  }

  // Pre-load buttons to reduce N+1 queries
  async #preloadButtonsForItems(operations, userId, transaction) {
    const buttonUuids = new Set();
    operations.forEach((op) => {
      const buttonUuid = op.buttonUuid;

      if (buttonUuid) buttonUuids.add(buttonUuid);
    });

    if (buttonUuids.size > 0) {
      const buttons = await ButtonModel.findButtons(
        { button_uuid: { [Op.in]: Array.from(buttonUuids) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;
      this.#performanceMetrics.cacheHits += buttons.length;

      // Cache buttons for validation using both ID and UUID
      buttons.forEach((button) => {
        this.#buttonCache.set(button.id, button);
        this.#buttonCache.set(button.button_uuid, button);
      });
    }
  }

  // Helper to add item response data
  #addItemResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    const itemId = this.#itemIdMap.get(operation.localItemId) || null;
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: itemId,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: operation.localItemId || null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process tag operations with optimized bulk creates
  async #processBulkTagOperations(
    tagOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    // Process creates first with bulk operation
    if (tagOps.create.length > 0) {
      await this.#bulkCreateTags(
        tagOps.create,
        userId,
        transaction,
        responseData,
        abortController,
        req
      );
    }

    // Process updates and deletes individually
    const individualOps = [...tagOps.update, ...tagOps.delete];

    if (individualOps.length > 0) {
      // Pre-fetch all required items to reduce N+1 queries
      await this.#preloadItemsForTags(individualOps, userId, transaction);

      for (const operation of individualOps) {
        this.#checkRequestAborted(req);
        if (abortController.signal.aborted) {
          throw new Error('Request was aborted by client');
        }

        try {
          switch (operation.operationType) {
            case OPERATION_TYPES.EDIT_ITEM_TAG:
              await this.updateTagSync(operation, userId, transaction);
              break;
            case OPERATION_TYPES.DELETE_ITEM_TAG:
              await this.deleteTagSync(operation, userId, transaction);
              break;
          }
          this.#addTagResponse(operation, responseData);
        } catch (error) {
          error.operationIndex = operation.originalIndex;
          error.operationType = operation.operationType;
          throw error;
        }
      }
    }
  }

  // Bulk create tags for better performance
  async #bulkCreateTags(
    createOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    if (createOps.length === 0) return;

    // Pre-validate all items exist and cache them
    const itemIds = new Set();
    createOps.forEach((op) => {
      const itemId = op.payload.itemId || this.#itemIdMap.get(op.localItemId);
      if (itemId) itemIds.add(itemId);
    });

    if (itemIds.size > 0) {
      const items = await ItemModel.findItems(
        { id: { [Op.in]: Array.from(itemIds) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;

      // Cache items for validation
      items.forEach((item) => {
        this.#itemCache.set(item.id, item);
      });
    }

    const bulkPayload = [];
    for (const operation of createOps) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        throw new Error('Request was aborted by client');
      }

      const data = {
        ...operation.payload,
        userId,
        localTagId: operation.localTagId,
      };
      const itemId = data.itemId || this.#itemIdMap.get(operation.localItemId);
      const buttonId =
        data.buttonId || this.#buttonIdMap.get(operation.localButtonId);
      const item = this.#itemCache.get(itemId);

      if (!item) {
        const error = new Error(API.ITEM_NOT_FOUND);
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }

      const payload = {
        item_id: itemId,
        button_id: buttonId || item.button_id,
        tag_uuid: data.tagUuid,
        tag_type: data.tagType,
        tag_title: data.tagTitle,
        tag_value: data.tagValue,
        tag_time_stamp: data.tagTimeStamp,
        _localTagId: operation.localTagId,
        _originalIndex: operation.originalIndex,
      };

      bulkPayload.push(payload);
    }

    try {
      // Use Sequelize bulkCreate directly on the schema
      const { Tag: TagSchema } = require('../../Database/Schemas');
      const createdTags = await TagSchema.bulkCreate(bulkPayload, {
        transaction,
        returning: true,
        validate: true,
      });

      this.#performanceMetrics.bulkOperations++;
      this.#performanceMetrics.dbQueries++;

      // Map created tags to local IDs and add responses
      const tagsArray = Array.isArray(createdTags)
        ? createdTags
        : [createdTags];
      for (let i = 0; i < tagsArray.length; i++) {
        const tag = tagsArray[i];
        const originalOp = createOps[i];

        this.#tagIdMap.set(originalOp.localTagId, tag.id);

        responseData.push({
          operationType: originalOp.operationType,
          syncId: originalOp.syncId,
          btnId: tag.button_id,
          alarmId: this.#alarmIdMap.get(tag.button_id) || null,
          itemId: tag.item_id,
          tagId: tag.id,
          localButtonId: originalOp.localButtonId,
          localItemId: originalOp.localItemId,
          localTagId: originalOp.localTagId,
          originalIndex: originalOp.originalIndex,
        });
      }
    } catch (error) {
      // Fallback to individual creates if bulk fails
      for (const operation of createOps) {
        try {
          await this.createTagSync(operation, userId, transaction);
          this.#addTagResponse(operation, responseData);
        } catch (individualError) {
          individualError.operationIndex = operation.originalIndex;
          individualError.operationType = operation.operationType;
          throw individualError;
        }
      }
    }
  }

  // Pre-load items to reduce N+1 queries for tags
  async #preloadItemsForTags(operations, userId, transaction) {
    const itemIds = new Set();
    operations.forEach((op) => {
      const itemId = op.payload.itemId || this.#itemIdMap.get(op.localItemId);
      if (itemId) itemIds.add(itemId);
    });

    if (itemIds.size > 0) {
      const items = await ItemModel.findItems(
        { id: { [Op.in]: Array.from(itemIds) }, user_id: userId },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;
      this.#performanceMetrics.cacheHits += items.length;

      items.forEach((item) => {
        this.#itemCache.set(item.id, item);
      });
    }
  }

  // Helper to add tag response data
  #addTagResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    const itemId = this.#itemIdMap.get(operation.localItemId) || null;
    const tagId = this.#tagIdMap.get(operation.localTagId) || null;
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: itemId,
      tagId: tagId,
      localButtonId: operation.localButtonId || null,
      localItemId: operation.localItemId || null,
      localTagId: operation.localTagId || null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process alarm operations
  async #processBulkAlarmOperations(
    alarmOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    console.log('[SyncController] Processing alarm operations...');

    for (const operation of alarmOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn(
          '[SyncController] Request aborted during alarm operation.'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Updating alarm sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}`
        );
        await this.updateAlarmSync(operation, userId, transaction);
        this.#addAlarmResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully updated alarm sync for localButtonId: ${operation.localButtonId}.`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error updating alarm sync for localButtonId: ${operation.localButtonId}, syncId: ${operation.syncId}:`,
          error.message
        );
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }
    }
    console.log('[SyncController] Finished processing alarm operations.');
  }

  // Helper to add alarm response data
  #addAlarmResponse(operation, responseData) {
    const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
    console.log(
      `[SyncController] Adding alarm response for operationType: ${operation.operationType}, syncId: ${operation.syncId}, buttonId: ${buttonId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: buttonId,
      alarmId: this.#alarmIdMap.get(buttonId) || null,
      itemId: null,
      tagId: null,
      localButtonId: operation.localButtonId || null,
      localItemId: null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  // Bulk process sequence operations
  async #processBulkSequenceOperations(
    sequenceOps,
    userId,
    transaction,
    responseData,
    abortController,
    req
  ) {
    console.log('[SyncController] Processing sequence operations...');
    for (const operation of sequenceOps.update) {
      this.#checkRequestAborted(req);
      if (abortController.signal.aborted) {
        console.warn(
          '[SyncController] Request aborted during sequence operation.'
        );
        throw new Error('Request was aborted by client');
      }

      try {
        console.log(
          `[SyncController] Changing button sequence sync for syncId: ${operation.syncId}`
        );
        await this.changeButtonSequenceSync(operation, userId, transaction);
        this.#addSequenceResponse(operation, responseData);
        console.log(
          `[SyncController] Successfully changed button sequence sync for syncId: ${operation.syncId}.`
        );
      } catch (error) {
        console.error(
          `[SyncController] Error changing button sequence sync for syncId: ${operation.syncId}:`,
          error.message
        );
        error.operationIndex = operation.originalIndex;
        error.operationType = operation.operationType;
        throw error;
      }
    }
    console.log('[SyncController] Finished processing sequence operations.');
  }

  // Helper to add sequence response data
  #addSequenceResponse(operation, responseData) {
    console.log(
      `[SyncController] Adding sequence response for operationType: ${operation.operationType}, syncId: ${operation.syncId}.`
    );
    responseData.push({
      operationType: operation.operationType,
      syncId: operation.syncId,
      btnId: null,
      alarmId: null,
      itemId: null,
      tagId: null,
      localButtonId: null,
      localItemId: null,
      localTagId: null,
      originalIndex: operation.originalIndex,
    });
  }

  async batchSync(req, res) {
    this.#resetMaps();
    this.#performanceMetrics.startTime = Date.now();

    const { operations } = req.body;
    const userId = req.userId;
    this.#performanceMetrics.totalOperations = operations.length;

    const { controller: abortController, cleanup } =
      this.#createAbortController(req);

    // console.log(
    //   `[SyncController] Starting batch sync with ${operations.length} operations for user ${userId}`
    // );

    // Check database connection before starting
    if (!(await this.#checkDatabaseConnection())) {
      console.error('[SyncController] Database connection not available');
      cleanup();
      return res.handler.serverError({
        message: 'Database connection not available. Please try again later.',
        code: 'DATABASE_UNAVAILABLE',
        retryable: true,
        timestamp: new Date().toISOString(),
      });
    }

    // Use READ_COMMITTED isolation level for better performance
    const transaction = await ButtonModel.getTransaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    // console.log('[SyncController] Database transaction started.');

    try {
      console.time('batchSync');

      // Group operations by type for potential bulk processing
      const groupedOperations = this.#groupOperationsByType(operations);
      console.log('[SyncController] Operations grouped successfully.');

      // Process operations in dependency order to maintain referential integrity
      const processedResults = await this.#processOperationsOptimized(
        groupedOperations,
        userId,
        transaction,
        abortController,
        req
      );
      console.log('[SyncController] All operations processed.');

      await transaction.commit();
      console.log('[SyncController] Database transaction committed.');

      this.#performanceMetrics.endTime = Date.now();
      const duration =
        this.#performanceMetrics.endTime - this.#performanceMetrics.startTime;

      console.timeEnd('batchSync');
      console.log(
        `[SyncController] Batch sync completed: ${operations.length} operations in ${duration}ms`
      );

      // Enhanced performance summary
      console.log('[SyncController] Performance Summary:');
      console.log(
        `- Total operations: ${this.#performanceMetrics.totalOperations}`
      );
      console.log(`- Total time: ${duration}ms`);
      console.log(`- Database queries: ${this.#performanceMetrics.dbQueries}`);
      console.log(
        `- Bulk operations: ${this.#performanceMetrics.bulkOperations}`
      );
      console.log(`- Cache hits: ${this.#performanceMetrics.cacheHits}`);
      console.log(
        `- Parallel operations: ${this.#performanceMetrics.parallelOperations}`
      );
      console.log(
        `- Average time per operation: ${(
          duration / this.#performanceMetrics.totalOperations
        ).toFixed(2)}ms`
      );
      console.log(
        `- Operations per second: ${(
          (this.#performanceMetrics.totalOperations / duration) *
          1000
        ).toFixed(2)}`
      );
      console.log(
        `[SyncController] Performance: ${
          this.#performanceMetrics.dbQueries
        } DB queries, ${
          this.#performanceMetrics.bulkOperations
        } bulk operations`
      );

      cleanup();
      return res.handler.success(null, processedResults);
    } catch (error) {
      console.error(
        '[SyncController] Error in batch sync, rolling back transaction:',
        error
      );

      // Handle database connection errors gracefully
      let rollbackError = null;
      try {
        await transaction.rollback();
        console.log(
          '[SyncController] Database transaction rolled back successfully.'
        );
      } catch (rollbackErr) {
        rollbackError = rollbackErr;
        console.error(
          '[SyncController] Transaction rollback failed:',
          rollbackErr
        );

        // If rollback fails due to connection loss, log it but don't crash
        if (
          rollbackErr.code === 'PROTOCOL_CONNECTION_LOST' ||
          rollbackErr.code === 'ER_SERVER_SHUTDOWN' ||
          rollbackErr.parent?.code === 'PROTOCOL_CONNECTION_LOST' ||
          rollbackErr.parent?.code === 'ER_SERVER_SHUTDOWN'
        ) {
          console.warn(
            '[SyncController] Database connection lost during rollback - transaction may be auto-rolled back by server'
          );
        }
      }

      cleanup();

      // Enhanced error response with context
      const errorResponse = {
        message: this.#getErrorMessage(error),
        code: this.#getErrorCode(error),
        operationIndex: error.operationIndex || null,
        operationType: error.operationType || null,
        timestamp: new Date().toISOString(),
        retryable: this.#isRetryableError(error),
        rollbackFailed: rollbackError !== null,
      };

      return res.handler.serverError(errorResponse);
    }
  }

  // async operationHandler(operation, userId, transaction) {
  //   const {
  //     operationType,
  //     payload,
  //     syncId,
  //     localButtonId,
  //     localItemId,
  //     localTagId,
  //   } = operation;
  //   console.log(
  //     `[SyncController] Handling single operation: ${operationType}, syncId: ${syncId}`
  //   );
  //   try {
  //     const opData = {
  //       ...payload,
  //       userId,
  //       localButtonId,
  //       localItemId,
  //       localTagId,
  //     };

  //     switch (operationType) {
  //       case OPERATION_TYPES.ADD_BUTTON:
  //         console.log(
  //           `[SyncController] Calling createButtonSync for syncId: ${syncId}`
  //         );
  //         await this.createButtonSync(opData, transaction);
  //         console.log(
  //           `[SyncController] createButtonSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.EDIT_BUTTON:
  //         console.log(
  //           `[SyncController] Calling updateButtonSync for syncId: ${syncId}`
  //         );
  //         await this.updateButtonSync(opData, transaction);
  //         console.log(
  //           `[SyncController] updateButtonSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.DELETE_BUTTON:
  //         console.log(
  //           `[SyncController] Calling deleteButtonSync for syncId: ${syncId}`
  //         );
  //         await this.deleteButtonSync(opData, transaction);
  //         console.log(
  //           `[SyncController] deleteButtonSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.CREATE_COUNT_BUTTON_ITEM:
  //       case OPERATION_TYPES.CREATE_DURATION_BUTTON_ITEM:
  //       case OPERATION_TYPES.CREATE_VALUE_BUTTON_ITEM:
  //         console.log(
  //           `[SyncController] Calling createItemSync for syncId: ${syncId}`
  //         );
  //         await this.createItemSync(opData, transaction);
  //         console.log(
  //           `[SyncController] createItemSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
  //       case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
  //       case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
  //         console.log(
  //           `[SyncController] Calling updateItemSync for syncId: ${syncId}`
  //         );
  //         await this.updateItemSync(opData, transaction);
  //         console.log(
  //           `[SyncController] updateItemSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
  //       case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
  //       case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
  //         console.log(
  //           `[SyncController] Calling deleteItemSync for syncId: ${syncId}`
  //         );
  //         await this.deleteItemSync(opData, transaction);
  //         console.log(
  //           `[SyncController] deleteItemSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.CREATE_ITEM_TAG:
  //         console.log(
  //           `[SyncController] Calling createTagSync for syncId: ${syncId}`
  //         );
  //         await this.createTagSync(opData, transaction);
  //         console.log(
  //           `[SyncController] createTagSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.EDIT_ITEM_TAG:
  //         console.log(
  //           `[SyncController] Calling updateTagSync for syncId: ${syncId}`
  //         );
  //         await this.updateTagSync(opData, transaction);
  //         console.log(
  //           `[SyncController] updateTagSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.DELETE_ITEM_TAG:
  //         console.log(
  //           `[SyncController] Calling deleteTagSync for syncId: ${syncId}`
  //         );
  //         await this.deleteTagSync(opData, transaction);
  //         console.log(
  //           `[SyncController] deleteTagSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.UPDATE_BUTTON_ALARM:
  //         console.log(
  //           `[SyncController] Calling updateAlarmSync for syncId: ${syncId}`
  //         );
  //         await this.updateAlarmSync(opData, transaction);
  //         console.log(
  //           `[SyncController] updateAlarmSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       case OPERATION_TYPES.CHANGE_BUTTON_SEQUENCE:
  //         console.log(
  //           `[SyncController] Calling changeButtonSequenceSync for syncId: ${syncId}`
  //         );
  //         await this.changeButtonSequenceSync(opData, transaction);
  //         console.log(
  //           `[SyncController] changeButtonSequenceSync completed for syncId: ${syncId}`
  //         );
  //         break;

  //       default:
  //         console.warn(
  //           `[SyncController] Invalid operationType received: ${operationType} for syncId: ${syncId}`
  //         );
  //         throw new Error(`Invalid operationType: ${operationType}`);
  //     }
  //     console.log(
  //       `[SyncController] Single operation ${operationType} for syncId: ${syncId} completed successfully.`
  //     );
  //   } catch (error) {
  //     console.error(
  //       `[SyncController] Error in operationHandler for operationType: ${operationType}, syncId: ${syncId}:`,
  //       error.message
  //     );
  //     throw error;
  //   }
  // }

  // async createButtonSync(operation, userId, transaction) {
  //   console.log(
  //     `[SyncController] Initiating createButtonSync for localButtonId: ${operation.localButtonId}.`
  //   );
  //   const data = {
  //     ...operation.payload,
  //     userId,
  //     localButtonId: operation.localButtonId,
  //   };
  //   const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

  //   if (!buttonType) {
  //     console.error(
  //       `[SyncController] Create button failed: Invalid button type for localButtonId: ${operation.localButtonId}.`
  //     );
  //     throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
  //   }

  //   const payload = {
  //     user_id: userId,
  //     button_uuid: data.buttonUuid,
  //     button_name: data.buttonName,
  //     button_color: data.buttonColor,
  //     button_type: buttonType,
  //     button_shape: data.buttonShape,
  //     button_summery_calculation: JSON.stringify(data.buttonSummeryCalculation),
  //     button_sequence: data.buttonSequence,
  //   };

  //   if (buttonType === BUTTON_TYPES.COUNT) {
  //     payload.count_inc = Number(data.countInc);
  //   } else if (buttonType === BUTTON_TYPES.VALUE) {
  //     payload.value_unit = data.valueUnit || null;
  //     payload.value_unit_description = data.valueUnitDescription || null;
  //     payload.value_item_name = data.valueItemName;
  //   }

  //   if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
  //   if (data.textNoteTag !== undefined)
  //     payload.text_note_tag = data.textNoteTag;
  //   if (data.locationTag !== undefined) payload.location_tag = data.locationTag;

  //   console.log(
  //     `[SyncController] Creating button in DB with payload for localButtonId: ${operation.localButtonId}.`
  //   );
  //   const button = await ButtonModel.createButton(payload, { transaction });
  //   this.#performanceMetrics.dbQueries++;

  //   if (!button) {
  //     console.error(
  //       `[SyncController] Button not created for localButtonId: ${operation.localButtonId}.`
  //     );
  //     throw new Error('Button not created');
  //   }

  //   console.log(
  //     `[SyncController] Button created with ID: ${button.id}. Creating associated alarm.`
  //   );
  //   // Create alarm for the button using helper method
  //   await this.#createAlarmForButton(button, userId, transaction);

  //   this.#alarmIdMap?.set(button.id, button.id);
  //   this.#buttonIdMap?.set(operation.localButtonId, button.id);
  //   console.log(
  //     `[SyncController] createButtonSync completed for localButtonId: ${operation.localButtonId}, new button ID: ${button.id}.`
  //   );
  // }

  async updateButtonSync(operation, userId, transaction) {
    console.log(
      `[SyncController] Initiating updateButtonSync for localButtonId: ${operation.localButtonId}, buttonId: ${operation.buttonId}.`
    );
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
    };

    // Check if either buttonId or localButtonId exists
    if (!data.buttonId && !data.localButtonId) {
      console.error(
        `[SyncController] Update button failed: Neither buttonId nor localButtonId provided for operation.`
      );
      throw new Error(
        'Either buttonId or localButtonId is required for button update'
      );
    }

    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update button failed: Button not found in map for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType) {
      console.error(
        `[SyncController] Update button failed: Invalid button type for buttonId: ${buttonId}.`
      );
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    // Get the existing button
    console.log(
      `[SyncController] Finding existing button with ID: ${buttonId} for update.`
    );
    const button = await ButtonModel.findButton(
      {
        [Op.or]: {
          id: buttonId,
          button_uuid: buttonUuid,
        },
        user_id: data.userId,
      },
      false,
      { transaction }
    );
    if (!button) {
      console.error(
        `[SyncController] Update button failed: Button with ID: ${buttonId} not found.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    console.log(`[SyncController] Button with ID: ${buttonId} found.`);

    const payload = {
      button_name: data.buttonName,
      button_color: data.buttonColor,
      button_shape: data.buttonShape,
      button_summery_calculation: JSON.stringify(data.buttonSummeryCalculation),
      button_sequence: data.buttonSequence,
    };

    // Check if button type is changed
    if (buttonType !== undefined && buttonType !== button.button_type) {
      console.log(
        `[SyncController] Button type change detected for button ID: ${buttonId}. Checking for associated items.`
      );
      // Check if items exist for this button
      const items = await ItemModel.countItems({ button_id: buttonId }, false, {
        transaction,
      });
      if (items > 0) {
        console.error(
          `[SyncController] Button type change failed: Button ID: ${buttonId} has item history.`
        );
        throw new Error(API.BUTTON_HAVE_ITEM_HISTORY);
      }
      console.log(
        `[SyncController] No items found for button ID: ${buttonId}. Proceeding with type change.`
      );
      payload.button_type = buttonType;

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.count_inc = null;
      } else if (buttonType === BUTTON_TYPES.DURATION) {
        payload.count_inc = null;
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      }
    }

    // Add type-specific fields based on the current button type
    if (button.button_type === BUTTON_TYPES.COUNT) {
      payload.count_inc = Number(data.countInc);
    } else if (button.button_type === BUTTON_TYPES.VALUE) {
      payload.value_unit = data.valueUnit || null;
      payload.value_unit_description = data.valueUnitDescription || null;
      payload.value_item_name = data.valueItemName;
    }

    // Add optional tag fields
    if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
    if (data.textNoteTag !== undefined)
      payload.text_note_tag = data.textNoteTag;
    if (data.locationTag !== undefined) payload.location_tag = data.locationTag;

    // Update the button
    console.log(`[SyncController] Updating button in DB for ID: ${buttonId}.`);
    await ButtonModel.updateButton({ id: buttonId, user_id: userId }, payload, {
      transaction,
    });
    this.#performanceMetrics.dbQueries++;
    console.log(
      `[SyncController] Button ID: ${buttonId} updated successfully.`
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(operation.localButtonId, button.id);
    console.log(
      `[SyncController] updateButtonSync completed for localButtonId: ${operation.localButtonId}, button ID: ${buttonId}.`
    );
  }

  async deleteButtonSync(operation, userId, transaction) {
    console.log(
      `[SyncController] Initiating deleteButtonSync for localButtonId: ${operation.localButtonId}, buttonId: ${operation.buttonId}.`
    );
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
    };
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Delete button failed: Button not found in map for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding button with ID: ${buttonId} for deletion.`
    );
    const button = await ButtonModel.findButton(
      {
        [Op.or]: {
          id: buttonId,
          button_uuid: buttonUuid,
        },
        user_id: userId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!button) {
      console.error(
        `[SyncController] Delete button failed: Button with ID: ${buttonId} not found.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    if (button.is_deleted) {
      console.warn(
        `[SyncController] Delete button failed: Button with ID: ${buttonId} is already marked as deleted.`
      );
      throw new Error(API.BUTTON_ALREADY_DELETED);
    }
    console.log(
      `[SyncController] Button with ID: ${buttonId} found and is not deleted. Marking as deleted.`
    );

    await ButtonModel.updateButton(
      { id: buttonId, user_id: userId },
      { is_deleted: true },
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;
    console.log(
      `[SyncController] Button ID: ${buttonId} successfully marked as deleted.`
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(operation.localButtonId, button.id);
    console.log(
      `[SyncController] deleteButtonSync completed for localButtonId: ${operation.localButtonId}, button ID: ${buttonId}.`
    );
  }

  /* 
  async createItemSync(operation, userId, transaction) {
    console.log(
      `[SyncController] Initiating createItemSync for localItemId: ${operation.localItemId}, localButtonId: ${operation.localButtonId}.`
    );
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
    };
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Create item failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding button with ID: ${buttonId} for item creation.`
    );
    const button = await ButtonModel.findButton(
      {
        [Op.or]: {
          id: buttonId,
          button_uuid: buttonUuid,
        },
        user_id: userId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!button) {
      console.log(
        '[SyncController] Button ID not found from operation data:',
        buttonId
      );
      console.log('[SyncController] Operation data:', data);
      console.log('[SyncController] Current button ID map:', this.#buttonIdMap);
      console.error(
        `[SyncController] Create item failed: Button with ID: ${buttonId} not found for user: ${userId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    console.log(`[SyncController] Button with ID: ${buttonId} found.`);

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType || buttonType !== button.button_type) {
      console.error(
        `[SyncController] Create item failed: Button type mismatch for button ID: ${buttonId}. Expected: ${button.button_type}, Received: ${buttonType}.`
      );
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    const payload = {
      button_id: buttonId,
      user_id: userId,
      item_uuid: data.itemUuid,
      display_time: data.displayTime,
      display_date: data.displayDate,
      display_month_year: data.displayMonthYear,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_value = data.countIncrement;
      payload.count_time_stamp = data.countTimeStamp;
    } else if (buttonType === BUTTON_TYPES.DURATION) {
      payload.duration_start_time_stamp = data.durationStartTimeStamp;
      payload.duration_stop_time_stamp = data.durationStopTimeStamp;
      payload.duration_time_ms = data.durationTimeMs;
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.item_name = data.itemName;
      payload.item_value = data.itemValue;
      payload.value_unit = data.valueUnit;
      payload.value_time_stamp = data.valueTimeStamp;
    }

    console.log(
      `[SyncController] Creating item in DB with payload for localItemId: ${operation.localItemId}, buttonId: ${buttonId}.`
    );
    const item = await ItemModel.createItem(payload, { transaction });
    this.#performanceMetrics.dbQueries++;

    if (!item) {
      console.error(
        `[SyncController] Item not created for localItemId: ${operation.localItemId}, buttonId: ${buttonId}.`
      );
      throw new Error('Item not created');
    }
    console.log(
      `[SyncController] Item created with ID: ${item.id} for localItemId: ${operation.localItemId}.`
    );

    this.#itemIdMap?.set(operation.localItemId, item.id);
    this.#buttonIdMap?.set(
      operation.localButtonId,
      buttonId || button.button_id
    );
    console.log(
      `[SyncController] createItemSync completed for localItemId: ${operation.localItemId}, new item ID: ${item.id}.`
    );
  }
 */

  async updateItemSync(operation, userId, transaction) {
    console.log(
      `[SyncController] Initiating updateItemSync for localItemId: ${operation.localItemId}, itemId: ${operation.itemId}, localButtonId: ${operation.localButtonId}.`
    );
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
    };
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const itemUuid = data.itemUuid;

    if (!buttonId) {
      console.error(
        `[SyncController] Update item failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId) {
      console.error(
        `[SyncController] Update item failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    // Try to get button from cache first using button_uuid
    let button =
      this.#buttonCache.get(buttonUuid) || this.#buttonCache.get(buttonId);
    if (!button) {
      button = await ButtonModel.findButton(
        {
          [Op.or]: {
            id: buttonId,
            button_uuid: buttonUuid,
          },
          user_id: userId,
        },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;

      // Cache the button for future use using both ID and UUID
      if (button) {
        this.#buttonCache.set(button.id, button);
        this.#buttonCache.set(button.button_uuid, button);
      }
    } else {
      this.#performanceMetrics.cacheHits++;
    }

    if (!button) {
      console.error(
        `[SyncController] Update item failed: Button with ID: ${buttonId} not found for user: ${userId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    console.log(`[SyncController] Button with ID: ${buttonId} found.`);

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType || buttonType !== button.button_type) {
      console.error(
        `[SyncController] Update item failed: Button type mismatch for button ID: ${buttonId}. Expected: ${button.button_type}, Received: ${buttonType}.`
      );
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    console.log(`[SyncController] Finding item with ID: ${itemId} for update.`);
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          id: itemId,
          item_uuid: itemUuid,
        },
        user_id: userId,
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!item) {
      console.error(
        `[SyncController] Update item failed: Item with ID: ${itemId} not found for button ID: ${buttonId}, user ID: ${userId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    console.log(`[SyncController] Item with ID: ${itemId} found.`);

    const payload = {
      display_time: data.displayTime,
      display_date: data.displayDate,
      display_month_year: data.displayMonthYear,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_value = data.countIncrement;
      payload.count_time_stamp = data.countTimeStamp;
    } else if (buttonType === BUTTON_TYPES.DURATION) {
      payload.duration_start_time_stamp = data.durationStartTimeStamp;
      payload.duration_time_ms = data.durationTimeMs;
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.item_value = data.itemValue;
      payload.value_time_stamp = data.valueTimeStamp;
    }

    console.log(
      `[SyncController] Updating item in DB for ID: ${itemId}, buttonId: ${buttonId}.`
    );
    await ItemModel.updateItem(
      {
        id: itemId,
        user_id: userId,
        button_id: buttonId,
      },
      payload,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;
    console.log(`[SyncController] Item ID: ${itemId} updated successfully.`);

    this.#itemIdMap?.set(operation.localItemId, item.id);
    this.#buttonIdMap?.set(operation.localButtonId, buttonId || item.button_id);
    console.log(
      `[SyncController] updateItemSync completed for localItemId: ${operation.localItemId}, item ID: ${itemId}.`
    );
  }

  async deleteItemSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
    };
    console.log(
      `[SyncController] Initiating deleteItemSync for localItemId: ${data.localItemId}, itemId: ${data.itemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const itemUuid = data.itemUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Delete item failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Delete item failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding item with ID: ${itemId} for deletion.`
    );
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          id: itemId,
          item_uuid: itemUuid,
        },
        user_id: userId,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!item) {
      console.error(
        `[SyncController] Delete item failed: Item with ID: ${itemId} not found for button ID: ${buttonId}, user ID: ${userId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    if (item.is_deleted) {
      console.warn(
        `[SyncController] Delete item failed: Item with ID: ${itemId} is already marked as deleted.`
      );
      throw new Error(API.ITEM_ALREADY_DELETED);
    }
    console.log(
      `[SyncController] Item with ID: ${itemId} found and is not deleted. Marking as deleted.`
    );

    await ItemModel.updateItem(
      {
        [Op.or]: {
          id: itemId,
          item_uuid: itemUuid,
        },
        user_id: userId,
      },
      { is_deleted: true },
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;
    console.log(
      `[SyncController] Item ID: ${itemId} successfully marked as deleted.`
    );

    this.#itemIdMap?.set(operation.localItemId, item.id);
    this.#buttonIdMap?.set(operation.localButtonId, buttonId || item.button_id);
    console.log(
      `[SyncController] deleteItemSync completed for localItemId: ${data.localItemId}, item ID: ${itemId}.`
    );
  }

  async createTagSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
    };
    console.log(
      `[SyncController] Initiating createTagSync for localTagId: ${data.localTagId}, localItemId: ${data.localItemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const itemUuid = data.itemUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Create tag failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Create tag failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }

    // Verify item exists
    console.log(
      `[SyncController] Verifying item exists for itemId: ${itemId}, buttonId: ${buttonId}.`
    );
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          id: itemId,
          item_uuid: itemUuid,
        },
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    if (!item) {
      console.error(
        `[SyncController] Create tag failed: Item with ID: ${itemId} not found for button ID: ${buttonId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    console.log(`[SyncController] Item with ID: ${itemId} found.`);

    const payload = {
      item_id: itemId,
      button_id: buttonId,
      tag_type: data.tagType,
      tag_title: data.tagTitle,
      tag_value: data.tagValue,
      tag_time_stamp: data.tagTimeStamp,
    };

    console.log(
      `[SyncController] Creating tag in DB with payload for localTagId: ${data.localTagId}.`
    );
    const tag = await TagModel.createTag(payload, { transaction });
    this.#performanceMetrics.dbQueries++;

    if (!tag) {
      console.error(
        `[SyncController] Tag not created for localTagId: ${data.localTagId}, itemId: ${itemId}, buttonId: ${buttonId}.`
      );
      throw new Error('Tag not created');
    }
    console.log(
      `[SyncController] Tag created with ID: ${tag.id} for localTagId: ${data.localTagId}.`
    );

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId || item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId || item.button_id);
    console.log(
      `[SyncController] createTagSync completed for localTagId: ${data.localTagId}, new tag ID: ${tag.id}.`
    );
  }

  async updateTagSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
    };
    console.log(
      `[SyncController] Initiating updateTagSync for localTagId: ${data.localTagId}, tagId: ${data.tagId}, localItemId: ${data.localItemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const itemUuid = data.itemUuid;
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId);
    const tagUuid = data.tagUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update tag failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Update tag failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    if (!tagId && !tagUuid) {
      console.error(
        `[SyncController] Update tag failed: Tag not found for localTagId: ${data.localTagId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    // Verify item exists
    console.log(
      `[SyncController] Verifying item exists for itemId: ${itemId}, buttonId: ${buttonId}.`
    );
    const item = await ItemModel.findItem(
      {
        [Op.or]: {
          id: itemId,
          item_uuid: itemUuid,
        },
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    if (!item) {
      console.error(
        `[SyncController] Update tag failed: Item with ID: ${itemId} not found for button ID: ${buttonId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    console.log(`[SyncController] Item with ID: ${itemId} found.`);

    // Find and verify tag
    console.log(`[SyncController] Finding tag with ID: ${tagId} for update.`);
    const tag = await TagModel.findTag(
      {
        [Op.or]: {
          id: tagId,
          tag_uuid: tagUuid,
        },
        item_id: item.id,
        button_id: item.button_id,
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!tag) {
      console.error(
        `[SyncController] Update tag failed: Tag with ID: ${tagId} not found for button ID: ${buttonId}, item ID: ${itemId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }
    console.log(`[SyncController] Tag with ID: ${tagId} found.`);

    // Check if tag type matches
    if (tag.tag_type !== data.tagType) {
      console.error(
        `[SyncController] Update tag failed: Tag type mismatch for tag ID: ${tagId}. Expected: ${tag.tag_type}, Received: ${data.tagType}.`
      );
      throw new Error(API.TAG_TYPE_DO_NOT_MATCH);
    }

    // Check if it's a GPS tag (which can't be updated)
    if (tag.tag_type === TAG_TYPES.GPS) {
      console.error(
        `[SyncController] Update tag failed: Cannot update GPS tag with ID: ${tagId}.`
      );
      throw new Error(API.CANNOT_UPDATE_GPS_TAG);
    }

    const payload = {
      tag_title: data.tagTitle,
      tag_value: data.tagValue,
      tag_time_stamp: data.tagTimeStamp,
    };

    console.log(`[SyncController] Updating tag in DB for ID: ${tagId}.`);
    await TagModel.updateTag({ id: tagId }, payload, { transaction });
    this.#performanceMetrics.dbQueries++;
    console.log(`[SyncController] Tag ID: ${tagId} updated successfully.`);

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId || item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId || item.button_id);
    console.log(
      `[SyncController] updateTagSync completed for localTagId: ${data.localTagId}, tag ID: ${tagId}.`
    );
  }

  async deleteTagSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
      localItemId: operation.localItemId,
      localTagId: operation.localTagId,
    };
    console.log(
      `[SyncController] Initiating deleteTagSync for localTagId: ${data.localTagId}, tagId: ${data.tagId}, localItemId: ${data.localItemId}, localButtonId: ${data.localButtonId}.`
    );
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const itemUuid = data.itemUuid;
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId);
    const tagUuid = data.tagUuid;

    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Delete tag failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }
    if (!itemId && !itemUuid) {
      console.error(
        `[SyncController] Delete tag failed: Item not found for localItemId: ${data.localItemId}.`
      );
      throw new Error(API.ITEM_NOT_FOUND);
    }
    if (!tagId && !tagUuid) {
      console.error(
        `[SyncController] Delete tag failed: Tag not found for localTagId: ${data.localTagId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    // Find tag to verify it exists
    console.log(`[SyncController] Finding tag with ID: ${tagId} for deletion.`);
    const tag = await TagModel.findTag(
      {
        [Op.or]: {
          id: tagId,
          tag_uuid: tagUuid,
        },
      },
      false,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!tag) {
      console.error(
        `[SyncController] Delete tag failed: Tag with ID: ${tagId} not found for button ID: ${buttonId}, item ID: ${itemId}.`
      );
      throw new Error(API.TAG_NOT_FOUND);
    }

    if (tag.is_deleted) {
      console.warn(
        `[SyncController] Delete tag failed: Tag with ID: ${tagId} is already marked as deleted.`
      );
      throw new Error(API.TAG_ALREADY_DELETED);
    }
    console.log(
      `[SyncController] Tag with ID: ${tagId} found and is not deleted. Marking as deleted.`
    );

    // Mark tag as deleted
    await TagModel.updateTag(
      { id: tagId || tag.id },
      { is_deleted: true },
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;
    console.log(
      `[SyncController] Tag ID: ${tagId} successfully marked as deleted.`
    );

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId || tag.item_id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId || tag.button_id);
    console.log(
      `[SyncController] deleteTagSync completed for localTagId: ${data.localTagId}, tag ID: ${tagId}.`
    );
  }

  async updateAlarmSync(operation, userId, transaction) {
    const data = {
      ...operation.payload,
      userId,
      localButtonId: operation.localButtonId,
    };
    console.log(
      `[SyncController] Initiating updateAlarmSync for localButtonId: ${data.localButtonId}, buttonId: ${data.buttonId}, alarmId: ${data.alarmId}.`
    );
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const buttonUuid = data.buttonUuid;
    if (!buttonId && !buttonUuid) {
      console.error(
        `[SyncController] Update alarm failed: Button not found for localButtonId: ${data.localButtonId}.`
      );
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const alarmId = data.alarmId || this.#alarmIdMap.get(buttonId);
    if (!alarmId && !buttonUuid) {
      console.error(
        `[SyncController] Update alarm failed: Alarm not found for buttonId: ${buttonId}.`
      );
      throw new Error(API.ALARM_NOT_FOUND);
    }

    console.log(
      `[SyncController] Finding alarm with ID: ${alarmId} for update.`
    );
    const alarm = await AlarmModel.findAlarm(
      {
        [Op.or]: {
          id: alarmId,
          button_uuid: buttonUuid,
        },
        user_id: userId,
      },
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;

    if (!alarm) {
      console.error(
        `[SyncController] Update alarm failed: Alarm with ID: ${alarmId} not found for button ID: ${buttonId}.`
      );
      throw new Error(API.ALARM_NOT_FOUND);
    }
    console.log(`[SyncController] Alarm with ID: ${alarmId} found.`);

    const payload = {
      alarm_time: data.alarmTime,
      snooze_time: data.snoozeTime,
      repeat_daily: data.repeatDaily,
      is_ring_after: data.isRingAfter,
      is_ring_after_always: data.isRingAfterAlways,
      ring_after_time_stamp: data.ringAfterTimeStamp,
      ring_after_time_ms: data.ringAfterTimeMs,
      is_active: data.isActive,
    };

    console.log(
      `[SyncController] Updating alarm in DB for ID: ${alarmId}, buttonId: ${buttonId}.`
    );
    await AlarmModel.updateAlarm(
      { id: alarm.id, button_id: alarm.button_id, user_id: data.userId },
      payload,
      { transaction }
    );
    this.#performanceMetrics.dbQueries++;
    console.log(`[SyncController] Alarm ID: ${alarmId} updated successfully.`);

    this.#alarmIdMap?.set(buttonId, alarm.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
    console.log(
      `[SyncController] updateAlarmSync completed for button ID: ${buttonId}, alarm ID: ${alarmId}.`
    );
  }

  async changeButtonSequenceSync(operation, userId, transaction) {
    const data = { ...operation.payload, userId };
    console.log(
      `[SyncController] Initiating changeButtonSequenceSync for user ID: ${data.userId}.`
    );
    try {
      const { buttonSequence } = data;

      const buttonUuids = buttonSequence.map((sequence) => {
        // const buttonId =
        //   sequence.buttonId || this.#buttonIdMap.get(sequence.localButtonId);
        const buttonUuid = sequence.buttonUuid;

        if (!buttonUuid) {
          console.error(
            `[SyncController] Change button sequence failed: Button not found for localButtonId: ${sequence.localButtonId}.`
          );
          throw new Error(API.BUTTON_NOT_FOUND);
        }
        return buttonUuid;
      });
      console.log(
        `[SyncController] Extracted button UUIDs for sequence change: ${buttonUuids.join(
          ', '
        )}.`
      );

      console.log(
        `[SyncController] Counting buttons for user ID: ${userId} with provided IDs.`
      );
      const buttonsCount = await ButtonModel.countButtons(
        {
          user_id: userId,
          button_uuid: {
            [Op.in]: buttonUuids,
          },
        },
        false,
        { transaction }
      );
      this.#performanceMetrics.dbQueries++;

      if (!buttonsCount || buttonsCount !== buttonSequence.length) {
        console.error(
          `[SyncController] Change button sequence failed: Mismatch in button count. Found: ${buttonsCount}, Expected: ${buttonSequence.length}.`
        );
        throw new Error(API.BUTTON_NOT_FOUND);
      }
      console.log(`[SyncController] Button count matched: ${buttonsCount}.`);

      const sequences = buttonSequence.map((b) => b.sequence);
      if (new Set(sequences).size !== sequences.length) {
        console.error(
          `[SyncController] Change button sequence failed: Duplicate sequence found in payload.`
        );
        throw new Error(API.DUPLICATE_SEQUENCE_FOUND);
      }
      console.log(
        `[SyncController] No duplicate sequences found. Proceeding with updates.`
      );

      const updatePromises = buttonSequence.map((sequence) => {
        const buttonUuid = sequence.buttonUuid;
        console.log(
          `[SyncController] Preparing update for button UUID: ${buttonUuid} to sequence: ${sequence.sequence}.`
        );
        return ButtonModel.updateButton(
          {
            button_id: buttonUuid,
            user_id: userId,
          },
          { button_sequence: sequence.sequence },
          { transaction }
        );
      });

      console.log(
        `[SyncController] Executing all button sequence update promises.`
      );
      await Promise.all(updatePromises);
      this.#performanceMetrics.dbQueries += updatePromises.length;
      console.log(
        `[SyncController] All button sequence updates completed successfully.`
      );
    } catch (error) {
      console.error(
        `[SyncController] Error in changeButtonSequenceSync for user ID: ${data.userId}:`,
        error.message
      );
      throw error;
    }
    console.log(
      `[SyncController] changeButtonSequenceSync completed for user ID: ${data.userId}.`
    );
  }
}

module.exports = SyncController;
