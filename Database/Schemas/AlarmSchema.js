module.exports = (sequelize, DataTypes) => {
  const AlarmSchema = sequelize.define(
    'Alarm',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      button_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'buttons',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      button_uuid: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      alarm_time: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      snooze_time: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      repeat_daily: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_ring_after: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_ring_after_always: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      ring_after_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      ring_after_time_ms: {
        type: DataTypes.BIGINT,
        allowNull: true,
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
    },
    {
      tableName: 'alarms',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  AlarmSchema.associate = (models) => {
    AlarmSchema.belongsTo(models.User, {
      foreignKey: 'user_id',
    });

    AlarmSchema.belongsTo(models.Button, {
      foreignKey: 'button_id',
    });
  };

  return AlarmSchema;
};
