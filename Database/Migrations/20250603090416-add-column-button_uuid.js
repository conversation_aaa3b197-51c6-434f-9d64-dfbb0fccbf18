'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('buttons', 'button_uuid', {
      type: Sequelize.STRING,
      unique: true,
      allowNull: false,
    });

    await queryInterface.sequelize.addColumn('alarms', 'button_uuid', {
      type: Sequelize.STRING,
      allowNull: false,
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('buttons', 'button_uuid');

    await queryInterface.sequelize.removeColumn('alarms', 'button_uuid');
  }
};
